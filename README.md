# Spring Cloud Alibaba Nacos 配置中心演示项目

这是一个基于 Spring Cloud Alibaba 的配置中心演示项目，展示了如何使用 Nacos 作为配置中心读取配置文件内容。

## 技术栈

- **Spring Boot**: 2.6.13
- **Spring Cloud**: 2021.0.5
- **Spring Cloud Alibaba**: 2021.0.5.0
- **Nacos**: 配置中心和服务发现
- **Java**: 1.8

## 功能特性

### 1. 配置中心功能
- ✅ 从 Nacos 配置中心读取配置
- ✅ 支持配置热刷新（@RefreshScope）
- ✅ 支持多环境配置（dev、test、prod）
- ✅ 支持共享配置
- ✅ 配置属性类封装（@ConfigurationProperties）

### 2. 监控功能
- ✅ Spring Boot Actuator 健康检查
- ✅ 应用信息暴露
- ✅ 配置刷新端点

## 项目结构

```
src/main/java/com/demo/springclouddemo/
├── SpringclouddemoApplication.java     # 主启动类
├── controller/
│   ├── ConfigController.java           # 配置信息控制器
│   └── DemoController.java             # 演示控制器
└── config/
    ├── BusinessProperties.java         # 业务配置属性类
    └── DataSourceProperties.java       # 数据源配置属性类

src/main/resources/
├── bootstrap.yml                       # Bootstrap 配置文件
└── application.yml                     # 应用配置文件
```

## 配置说明

### Nacos 配置中心配置

在 Nacos 配置中心创建以下配置文件：

#### 1. 主配置文件：`nacosCloud-dev.yaml`
```yaml
# 数据源配置
datasource:
  username: nacos_user
  password: nacos_password
  url: **************************************

# 业务配置
business:
  config:
    timeout: 10000
    retry-count: 5
    enable-cache: true
    batch-size: 200
    max-connections: 100

# 索引配置
index: 100
```

#### 2. 共享配置文件：`common-config.yaml`
```yaml
# 公共配置
logging:
  level:
    root: INFO
    com.demo: DEBUG

# 公共业务配置
common:
  app-version: 1.0.0
  environment: development
```

## API 接口

### 配置相关接口

| 接口 | 方法 | 描述 |
|------|------|------|
| `/config/all` | GET | 获取所有配置信息 |
| `/config/business` | GET | 获取业务配置 |
| `/config/datasource` | GET | 获取数据源配置 |
| `/demo/config` | GET | 获取演示配置（新接口） |
| `/demo/testCloud` | GET | 获取演示配置（兼容旧接口） |



### 健康检查接口

| 接口 | 方法 | 描述 |
|------|------|------|
| `/actuator/health` | GET | 应用健康状态 |
| `/actuator/info` | GET | 应用信息 |
| `/actuator/refresh` | POST | 刷新配置 |
| `/actuator/nacos-config` | GET | Nacos 配置信息 |

## 快速开始

### 1. 环境准备

确保 Nacos 服务器已启动并可访问：
- Nacos 服务器地址：`***************:8848`
- Nacos 控制台：`http://***************:8848/nacos`

### 2. 配置 Nacos

在 Nacos 控制台创建配置：
- **Data ID**: `nacosCloud-dev.yaml`
- **Group**: `DEFAULT_GROUP`
- **配置格式**: `YAML`

### 3. 启动应用

```bash
# 编译项目
mvn clean compile

# 启动应用
mvn spring-boot:run
```

### 4. 验证功能

访问以下接口验证功能：

```bash
# 获取配置信息
curl http://localhost:8080/config/all

# 获取演示配置
curl http://localhost:8080/demo/config

# 健康检查
curl http://localhost:8080/actuator/health

# 刷新配置
curl -X POST http://localhost:8080/actuator/refresh
```

## 配置刷新测试

1. 修改 Nacos 中的配置
2. 访问 `/config/all` 接口查看配置是否自动更新
3. 或者调用 Actuator 的刷新接口：`POST /actuator/refresh`

## 注意事项

1. **版本兼容性**：确保 Spring Boot、Spring Cloud、Spring Cloud Alibaba 版本兼容
2. **网络连接**：确保应用能够访问 Nacos 服务器
3. **配置格式**：Nacos 中的配置文件格式要与 `file-extension` 配置一致
4. **命名空间**：注意 Nacos 的命名空间配置

## 故障排除

### 常见问题

1. **无法连接 Nacos**
   - 检查网络连接
   - 确认 Nacos 服务器地址和端口

2. **配置读取失败**
   - 检查 Data ID、Group、Namespace 配置
   - 确认配置文件格式

3. **服务注册失败**
   - 检查服务发现配置
   - 查看应用日志

### 日志调试

启用调试日志：
```yaml
logging:
  level:
    com.alibaba.nacos: DEBUG
    com.demo.springclouddemo: DEBUG
```
