package com.demo.springclouddemo.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 业务配置属性类
 * 
 * <AUTHOR>
 * @date 2025/7/7
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "business.config")
public class BusinessProperties {
    
    /**
     * 超时时间（毫秒）
     */
    private Integer timeout = 5000;
    
    /**
     * 重试次数
     */
    private Integer retryCount = 3;
    
    /**
     * 是否启用缓存
     */
    private Boolean enableCache = true;
    
    /**
     * 批处理大小
     */
    private Integer batchSize = 100;
    
    /**
     * 最大连接数
     */
    private Integer maxConnections = 50;
}
