package com.demo.springclouddemo.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 数据源配置属性类
 * 
 * <AUTHOR>
 * @date 2025/7/7
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "datasource")
public class DataSourceProperties {
    
    /**
     * 数据库用户名
     */
    private String username = "defaultUser";
    
    /**
     * 数据库密码
     */
    private String password = "defaultPassword";
    
    /**
     * 数据库连接URL
     */
    private String url = "********************************";
    
    /**
     * 数据库驱动类名
     */
    private String driverClassName = "com.mysql.cj.jdbc.Driver";
    
    /**
     * 最大连接数
     */
    private Integer maxActive = 20;
    
    /**
     * 初始连接数
     */
    private Integer initialSize = 5;
}
