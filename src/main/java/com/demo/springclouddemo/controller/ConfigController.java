package com.demo.springclouddemo.controller;

import com.demo.springclouddemo.config.BusinessProperties;
import com.demo.springclouddemo.config.DataSourceProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 配置信息控制器
 * 演示使用 @ConfigurationProperties 注解的配置类
 * 
 * <AUTHOR>
 * @date 2025/7/7
 */
@Slf4j
@RestController
@RequestMapping("/config")
public class ConfigController {

    @Autowired
    private BusinessProperties businessProperties;
    
    @Autowired
    private DataSourceProperties dataSourceProperties;
    
    @Value("${spring.application.name}")
    private String applicationName;
    
    @Value("${index:0}")
    private Integer index;

    /**
     * 获取所有配置信息
     */
    @GetMapping("/all")
    public Map<String, Object> getAllConfig() {
        log.info("获取所有配置信息");
        
        Map<String, Object> result = new HashMap<>();
        result.put("applicationName", applicationName);
        result.put("index", index);
        result.put("businessConfig", businessProperties);
        result.put("dataSourceConfig", dataSourceProperties);
        result.put("timestamp", LocalDateTime.now());
        
        return result;
    }

    /**
     * 获取业务配置
     */
    @GetMapping("/business")
    public Map<String, Object> getBusinessConfig() {
        log.info("获取业务配置信息");
        
        Map<String, Object> result = new HashMap<>();
        result.put("businessConfig", businessProperties);
        result.put("timestamp", LocalDateTime.now());
        
        return result;
    }

    /**
     * 获取数据源配置
     */
    @GetMapping("/datasource")
    public Map<String, Object> getDataSourceConfig() {
        log.info("获取数据源配置信息");
        
        Map<String, Object> result = new HashMap<>();
        result.put("dataSourceConfig", dataSourceProperties);
        result.put("timestamp", LocalDateTime.now());
        
        return result;
    }
}
