package com.demo.springclouddemo.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Nacos 配置中心演示控制器
 *
 * @RefreshScope 注解实现配置自动刷新
 * <AUTHOR>
 * @date 2025/7/7 11:12
 */
@Slf4j
@RefreshScope
@RestController
@RequestMapping("/demo")
public class DemoController {

    @Value("${index:0}")
    private Integer index;

    @Value("${datasource.username:defaultUser}")
    private String userName;

    @Value("${datasource.password:defaultPassword}")
    private String password;

    @Value("${datasource.url:********************************}")
    private String datasourceUrl;

    @Value("${business.config.timeout:5000}")
    private Integer timeout;

    @Value("${business.config.retry-count:3}")
    private Integer retryCount;

    @Value("${business.config.enable-cache:true}")
    private Boolean enableCache;

    /**
     * 获取配置信息
     */
    @GetMapping("/config")
    public Map<String, Object> getConfig() {
        log.info("获取配置信息，当前时间: {}", LocalDateTime.now());

        Map<String, Object> config = new HashMap<>();
        config.put("index", index);
        config.put("datasource", Map.of(
            "username", userName,
            "password", password,
            "url", datasourceUrl
        ));
        config.put("business", Map.of(
            "timeout", timeout,
            "retryCount", retryCount,
            "enableCache", enableCache
        ));
        config.put("timestamp", LocalDateTime.now());

        return config;
    }

    /**
     * 兼容旧接口
     */
    @RequestMapping("/testCloud")
    public String test() {
        return "{\"index\":\"" + index + "\", \"userName\":\"" + userName + "\",\"password\":\"" + password + "\"}";
    }
}
