package com.demo.springclouddemo.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 服务发现控制器
 * 
 * <AUTHOR>
 * @date 2025/7/7
 */
@Slf4j
@RestController
@RequestMapping("/discovery")
public class DiscoveryController {

    @Autowired
    private DiscoveryClient discoveryClient;

    /**
     * 获取所有服务列表
     */
    @GetMapping("/services")
    public Map<String, Object> getServices() {
        log.info("获取所有服务列表");
        
        List<String> services = discoveryClient.getServices();
        Map<String, Object> result = new HashMap<>();
        result.put("services", services);
        result.put("count", services.size());
        
        return result;
    }

    /**
     * 获取指定服务的实例列表
     */
    @GetMapping("/instances/{serviceName}")
    public Map<String, Object> getInstances(@PathVariable String serviceName) {
        log.info("获取服务 {} 的实例列表", serviceName);
        
        List<ServiceInstance> instances = discoveryClient.getInstances(serviceName);
        Map<String, Object> result = new HashMap<>();
        result.put("serviceName", serviceName);
        result.put("instances", instances);
        result.put("count", instances.size());
        
        return result;
    }

    /**
     * 获取当前服务信息
     */
    @GetMapping("/current")
    public Map<String, Object> getCurrentService() {
        log.info("获取当前服务信息");
        
        Map<String, Object> result = new HashMap<>();
        result.put("description", discoveryClient.description());
        result.put("order", discoveryClient.getOrder());
        
        return result;
    }
}
