# 配置spring.application.name和spring.profiles.active值，它是构成Nacos配置管理dataId字段一部分
# ${prefix}-${spring.profiles.active}.${file-extension}
# prefix 默认为 spring.application.name 的值，也可以通过配置项 spring.cloud.nacos.config.prefix来配置
# spring.profiles.active 为空时，对应的连接符 - 也将不存在，dataId 的拼接格式变成 ${prefix}.${file-extension}
spring.application.name=nacosCloud
spring.profiles.active=dev

# 配置Nacos server地址
spring.cloud.nacos.config.server-addr=192.168.100.128:8848
# 指定Nacos配置中心中配置文件的格式为properties文件(默认值为properties)
spring.cloud.nacos.config.file-extension=yaml
# 配置Nacos配置文件的分组
spring.cloud.nacos.config.group=DEFAULT_GROUP
# 配置Nacos配置文件的命名空间
spring.cloud.nacos.config.namespace=public

# shared-configs和extension-configs都表示拉取额外的配置文件
# 区别: extension-configs表示本应用特有的
#      shared-configs表示多个应用共享的
# 优先级
# extension-configs[2] > extension-configs[1] > extension-configs[0]
# shared-configs[2] > shared-configs[1] > shared-configs[0]
# 主配置 > extension-configs > shared-configs