spring:
  application:
    name: nacosCloud
  profiles:
    active: dev
  cloud:
    nacos:
      # Nacos 服务器地址
      server-addr: 192.168.100.128:8848
      # 配置中心配置
      config:
        server-addr: ${spring.cloud.nacos.server-addr}
        file-extension: yaml
        group: DEFAULT_GROUP
        namespace: public
        # 启用配置刷新
        refresh-enabled: true
        # 共享配置（可选）
        shared-configs:
          - data-id: common-config.yaml
            group: DEFAULT_GROUP
            refresh: true

# 日志配置
logging:
  level:
    com.alibaba.nacos: DEBUG
    com.demo.springclouddemo: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
