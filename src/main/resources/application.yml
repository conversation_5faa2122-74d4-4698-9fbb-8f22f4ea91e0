server:
  port: 8080

# Spring Boot Actuator 配置
management:
  endpoints:
    web:
      exposure:
        include: "health,info,refresh,nacos-config"
  endpoint:
    health:
      show-details: always

# 应用信息
info:
  app:
    name: ${spring.application.name}
    description: Spring Cloud Alibaba Nacos 配置中心演示
    version: 1.0.0

# 默认配置值（当 Nacos 配置中心不可用时使用）
datasource:
  username: ${datasource.username:defaultUser}
  password: ${datasource.password:defaultPassword}
  url: ${datasource.url:********************************}

index: ${index:0}

# 其他业务配置的默认值
business:
  config:
    timeout: ${business.config.timeout:5000}
    retry-count: ${business.config.retry-count:3}
    enable-cache: ${business.config.enable-cache:true}
